xfs: fshelp
fshelp:
videotest_checksum: video_fb font functional_test
relocator: mmap
procfs: archelp
offsetio:
gcry_seed: crypto
ufs2:
tr: extcmd
lsacpi: extcmd acpi
json:
gfxterm_background: extcmd gfxterm bitmap video video_colors bitmap_scale
file: offsetio extcmd elf macho
xnu_uuid: gcry_md5
part_sunpc:
lsapm:
extcmd:
echo: extcmd
btrfs: gzio lzopio raid6rec zstd
read:
priority_queue:
gcry_dsa: mpi pgp
ctz_test: functional_test
cbls: cbtable
zfs: gzio
progress: normal
minix:
setjmp:
macbless: disk
hfspluscomp: gzio hfsplus
gcry_rsa: mpi pgp
cat: extcmd
videotest: font video gfxmenu
sleep: extcmd normal
reiserfs: fshelp
lspci: extcmd pci
div:
video_cirrus: video_fb video pci
testload:
search_fs_uuid:
part_dvh:
gcry_md5: crypto
gcry_arcfour: crypto
ufs1_be:
part_acorn:
iorw: extcmd
gfxterm: font video
cpio_be: archelp
scsi:
memdisk:
crc64: crypto
ata: scsi
multiboot2: relocator lsapm boot net acpi linux video mmap vbe
gcry_idea: crypto
cpuid: extcmd
cmp_test: functional_test
video_fb:
sfs: fshelp
zfscrypt: extcmd zfs crypto pbkdf2 gcry_rijndael gcry_sha1
video_bochs: video_fb video pci
udf: fshelp
datehook: datetime
adler32: crypto
setjmp_test: setjmp functional_test
ntfscomp: ntfs
nativedisk:
freedos: relocator boot chain video
eval: normal
videoinfo: video
verifiers:
pbkdf2: crypto
password_pbkdf2: crypto pbkdf2 normal gcry_sha512
font: bufio video
datetime:
crypto:
vga: video_fb video
strtoull_test: functional_test
pcidump: extcmd pci
mdraid09_be: diskfilter
afsplitter: crypto
normal: extcmd verifiers datetime crypto boot net bufio gettext terminal
efiemu: cpuid crypto acpi gcry_crc smbios
tftp: net
gcry_whirlpool: crypto
fat: fshelp
setpci: extcmd pci
boot:
ufs1:
cpio: archelp
chain: relocator boot video
cbmemc: normal terminfo cbtable
tga: bufio bitmap
spkmodem: terminfo
odc: archelp
drivemap: extcmd boot mmap
usbserial_pl2303: usb serial usbserial_common
regexp: extcmd normal
password: crypto normal
ntfs: fshelp
net: priority_queue datetime boot bufio
loadenv: extcmd disk
gzio: gcry_crc
ehci: boot cs5536 usb pci
archelp:
kernel:
usbtest: usb
pxechain: relocator boot video pxe
gptsync: disk
date: datetime
xnu: relocator extcmd verifiers efiemu boot random bitmap video mmap bitmap_scale macho
mul_test: functional_test
bufio:
acpi: extcmd mmap
cmostest:
png: bufio bitmap
minix2:
cs5536: pci
linux: relocator verifiers normal boot video mmap vbe
gcry_des: crypto
gcry_blowfish: crypto
part_plan:
part_bsd: part_msdos
lvm: diskfilter
gcry_tiger: crypto
blocklist:
xzio: crypto
usb_keyboard: usb keylayouts
nilfs2: fshelp
lsmmap:
gcry_cast5: crypto
backtrace:
testspeed: extcmd normal
squash4: fshelp gzio xzio lzopio
msdospart: disk parttool
lzopio: crypto
gfxterm_menu: procfs video_fb font normal functional_test
gcry_sha256: crypto
vga_text:
usb: pci
pata: ata pci
hwmatch: normal regexp pci
affs: fshelp
syslinuxcfg: extcmd normal
random: acpi hexdump
part_amiga:
play:
part_apple:
mpi: crypto
keylayouts:
jpeg: bufio bitmap
gcry_rmd160: crypto
cbfs: archelp
time:
loopback: extcmd
disk:
at_keyboard: boot keylayouts
true:
test:
part_sun:
part_msdos:
morse:
http: net
part_gpt:
luks2: json crypto pbkdf2 afsplitter cryptodisk
elf:
diskfilter:
bitmap:
mda_text:
cmp:
uhci: usb pci
ls: extcmd datetime normal
search: extcmd search_fs_uuid search_fs_file search_label
raid6rec: diskfilter
minix3_be:
memrw: extcmd
cbtime: cbtable
zstd:
raid5rec: diskfilter
rdmsr: extcmd
hello: extcmd
video:
shift_test: functional_test
minix2_be:
hashsum: extcmd crypto normal
gdb: backtrace serial
video_colors:
mdraid1x: diskfilter
gcry_twofish: crypto
gcry_sha512: crypto
cryptodisk: procfs extcmd crypto
parttool: normal
ohci: boot cs5536 usb pci
mmap: boot
minix3:
gfxmenu: gfxterm font normal bitmap video video_colors bitmap_scale trig
dm_nv: diskfilter
search_fs_file:
pxe: boot net
luks: crypto pbkdf2 afsplitter cryptodisk
ldm: part_msdos part_gpt diskfilter
hfsplus: fshelp
hfs: fshelp
hexdump: extcmd
gettext:
gcry_rijndael: crypto
aout:
terminal:
probe: extcmd
plan9: relocator extcmd verifiers boot video
functional_test: extcmd btrfs video_fb video
minicmd:
geli: crypto pbkdf2 gcry_sha256 cryptodisk gcry_sha512
gcry_rfc2268: crypto
f2fs: fshelp
ext2: fshelp
bsd: relocator extcmd gcry_md5 cpuid crypto verifiers boot elf video mmap aout serial vbe
terminfo: extcmd
legacy_password_test: functional_test legacycfg
gcry_crc: crypto
gcry_camellia: crypto
bitmap_scale: bitmap
biosdisk:
part_dfly:
cbtable:
usbms: scsi usb
sleep_test: datetime functional_test
div_test: div functional_test
zfsinfo: zfs
wrmsr:
sendkey: extcmd boot
newc: archelp
multiboot: relocator lsapm boot net linux video mmap vbe
linux16: relocator boot linux video mmap
keystatus: extcmd
jfs:
gcry_md4: crypto
pci:
mdraid09: diskfilter
iso9660: fshelp
cmdline_cat_test: procfs video_fb font normal functional_test
bswap_test: functional_test
afs: fshelp
search_label:
reboot: relocator
pgp: extcmd verifiers crypto mpi gcry_sha1
xnu_uuid_test: functional_test
test_blockarg: extcmd normal
serial: extcmd terminfo
configfile: normal
cmosdump:
romfs: fshelp
minix_be:
help: extcmd normal
halt: extcmd acpi
gcry_sha1: crypto
usbserial_usbdebug: usb serial usbserial_common
usbserial_ftdi: usb serial usbserial_common
hdparm: extcmd hexdump
trig:
vbe: video_fb video
tar: archelp
legacycfg: gcry_md5 crypto normal password linux
ahci: ata boot pci
915resolution:
pbkdf2_test: pbkdf2 functional_test gcry_sha1
macho:
smbios: extcmd acpi
signature_test: procfs functional_test
gcry_serpent: crypto
bfs: fshelp
usbserial_common: usb serial
truecrypt: relocator boot gzio video mmap
ntldr: relocator boot chain video
exfctest: functional_test
exfat: fshelp
all_video: vbe vga video_bochs video_cirrus
