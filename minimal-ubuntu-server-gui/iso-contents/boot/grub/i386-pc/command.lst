*acpi: acpi
*all_functional_test: functional_test
*background_image: gfxterm_background
*cat: cat
*cpuid: cpuid
*crc: hashsum
*cryptomount: cryptodisk
*drivemap: drivemap
*echo: echo
*extract_syslinux_entries_configfile: syslinuxcfg
*extract_syslinux_entries_source: syslinuxcfg
*file: file
*functional_test: functional_test
*gettext: gettext
*halt: halt
*hashsum: hashsum
*hdparm: hdparm
*hello: hello
*help: help
*hexdump: hexdump
*inb: iorw
*inl: iorw
*inw: iorw
*keystatus: keystatus
*kfreebsd: bsd
*knetbsd: bsd
*kopenbsd: bsd
*list_env: loadenv
*load_env: loadenv
*loopback: loopback
*ls: ls
*lsacpi: lsacpi
*lspci: lspci
*md5sum: hashsum
*menuentry: normal
*pcidump: pcidump
*plan9: plan9
*probe: probe
*rdmsr: rdmsr
*read_byte: memrw
*read_dword: memrw
*read_word: memrw
*regexp: regexp
*save_env: loadenv
*search: search
*sendkey: sendkey
*serial: serial
*setpci: setpci
*sha1sum: hashsum
*sha256sum: hashsum
*sha512sum: hashsum
*sleep: sleep
*smbios: smbios
*submenu: normal
*syslinux_configfile: syslinuxcfg
*syslinux_source: syslinuxcfg
*terminfo: terminfo
*test_blockarg: test_blockarg
*testspeed: testspeed
*tr: tr
*trust: pgp
*verify_detached: pgp
*xnu_splash: xnu
*zfskey: zfscrypt
.: configfile
915resolution: 915resolution
[: test
authenticate: normal
background_color: gfxterm_background
backtrace: backtrace
badram: mmap
blocklist: blocklist
boot: boot
break: normal
cat: minicmd
cbmemc: cbmemc
chainloader: chain
clear: normal
cmosclean: cmostest
cmosdump: cmosdump
cmosset: cmostest
cmostest: cmostest
cmp: cmp
configfile: configfile
continue: normal
coreboot_boottime: cbtime
cutmem: mmap
date: date
distrust: pgp
dump: minicmd
efiemu_loadcore: efiemu
efiemu_prepare: efiemu
efiemu_unload: efiemu
eval: eval
exit: minicmd
export: normal
extract_entries_configfile: configfile
extract_entries_source: configfile
extract_legacy_entries_configfile: legacycfg
extract_legacy_entries_source: legacycfg
false: true
freedos: freedos
gdbstub: gdb
gdbstub_break: gdb
gdbstub_stop: gdb
gptsync: gptsync
help: minicmd
hexdump_random: random
hwmatch: hwmatch
initrd16: linux16
initrd: linux
keymap: keylayouts
kfreebsd_loadenv: bsd
kfreebsd_module: bsd
kfreebsd_module_elf: bsd
knetbsd_module: bsd
knetbsd_module_elf: bsd
kopenbsd_ramdisk: bsd
legacy_check_password: legacycfg
legacy_configfile: legacycfg
legacy_initrd: legacycfg
legacy_initrd_nounzip: legacycfg
legacy_kernel: legacycfg
legacy_password: legacycfg
legacy_source: legacycfg
linux16: linux16
linux: linux
list_trusted: pgp
loadfont: font
lsapm: lsapm
lscoreboot: cbls
lsfonts: font
lsmmap: lsmmap
lsmod: minicmd
macppcbless: macbless
mactelbless: macbless
module2: multiboot2
module: multiboot
multiboot2: multiboot2
multiboot: multiboot
nativedisk: nativedisk
net_add_addr: net
net_add_dns: net
net_add_route: net
net_bootp6: net
net_bootp: net
net_del_addr: net
net_del_dns: net
net_del_route: net
net_dhcp: net
net_get_dhcp_option: net
net_ipv6_autoconf: net
net_ls_addr: net
net_ls_cards: net
net_ls_dns: net
net_ls_routes: net
net_nslookup: net
normal: normal
normal_exit: normal
ntldr: ntldr
outb: iorw
outl: iorw
outw: iorw
parttool: parttool
password: password
password_pbkdf2: password_pbkdf2
play: play
pxechainloader: pxechain
read: read
reboot: reboot
return: normal
rmmod: minicmd
search.file: search_fs_file
search.fs_label: search_label
search.fs_uuid: search_fs_uuid
setparams: normal
shift: normal
source: configfile
terminal_input: terminal
terminal_output: terminal
test: test
testload: testload
time: time
true: true
truecrypt: truecrypt
usb: usbtest
vbeinfo: videoinfo
vbetest: videotest
videoinfo: videoinfo
videotest: videotest
write_byte: memrw
write_dword: memrw
write_word: memrw
wrmsr: wrmsr
xnu_devprop_load: xnu
xnu_kernel64: xnu
xnu_kernel: xnu
xnu_kext: xnu
xnu_kextdir: xnu
xnu_mkext: xnu
xnu_ramdisk: xnu
xnu_resume: xnu
xnu_uuid: xnu_uuid
zfs-bootfs: zfsinfo
zfsinfo: zfsinfo
