#cloud-config
autoinstall:
  version: 1
  
  # Locale and keyboard
  locale: en_US.UTF-8
  keyboard:
    layout: us
    variant: ''
  
  # Network configuration
  network:
    network:
      version: 2
      ethernets:
        any:
          match:
            name: "e*"
          dhcp4: true
          dhcp6: false
  
  # Storage configuration - use entire disk
  storage:
    layout:
      name: direct
    swap:
      size: 0
  
  # Identity configuration
  identity:
    hostname: minimal-ubuntu
    username: ubuntu
    password: '$6$rounds=4096$saltsalt$L9tjczoIVjIaLZL4HSWiFQWJjKJBWzVtQFJwO/Vo7.rMHgqmKfF8L6Rn.rLxjIJhd7EdAoq1QU2KXLmvF8E4/'  # password: ubuntu
    realname: Ubuntu User
  
  # SSH configuration
  ssh:
    install-server: true
    allow-pw: true
    authorized-keys: []
  
  # Package selection - minimal with LXQt GUI
  packages:
    # Base system
    - openssh-server
    - curl
    - wget
    - nano
    - vim-tiny
    - htop
    - net-tools
    - rsync
    - ufw
    - chrony
    - lshw
    - unzip

    # X11 and display server
    - xorg
    - xserver-xorg-video-all
    - xserver-xorg-input-all

    # LXQt desktop environment
    - lxqt-core
    - lxqt-config
    - lxqt-session
    - openbox
    - obconf-qt

    # Display manager
    - sddm
    - sddm-theme-breeze

    # Essential GUI applications
    - pcmanfm-qt
    - qterminal
    - featherpad
    - qpdfview
    - firefox
    - pavucontrol-qt

    # Fonts and themes
    - fonts-dejavu-core
    - fonts-liberation
    - breeze-icon-theme
    - adwaita-icon-theme

    # Audio support
    - pulseaudio
    - pulseaudio-utils
    - alsa-utils

    # Network management GUI
    - network-manager
    - network-manager-gnome

    # Archive manager
    - ark

    # System utilities
    - gparted
    - synaptic
    - software-properties-qt
  
  # Packages to remove
  package_update: false
  package_upgrade: false
  
  # User configuration
  user-data:
    users:
      - name: ubuntu
        groups: [adm, sudo]
        lock_passwd: false
        sudo: ['ALL=(ALL) NOPASSWD:ALL']
        shell: /bin/bash
  
  # Late commands for customization
  late-commands:
    # Remove unnecessary packages (keep network-manager for GUI)
    - curtin in-target --target=/target -- apt-get remove -y snapd
    - curtin in-target --target=/target -- apt-get autoremove -y
    - curtin in-target --target=/target -- apt-get autoclean
    
    # Configure firewall
    - curtin in-target --target=/target -- ufw --force enable
    - curtin in-target --target=/target -- ufw default deny incoming
    - curtin in-target --target=/target -- ufw default allow outgoing
    - curtin in-target --target=/target -- ufw allow ssh
    
    # Configure GUI services
    - curtin in-target --target=/target -- systemctl enable sddm
    - curtin in-target --target=/target -- systemctl enable network-manager
    - curtin in-target --target=/target -- systemctl enable chrony
    - curtin in-target --target=/target -- systemctl disable systemd-networkd
    - curtin in-target --target=/target -- systemctl disable systemd-resolved
    
    # Configure SDDM display manager
    - |
      mkdir -p /target/etc/sddm.conf.d
      cat > /target/etc/sddm.conf.d/autologin.conf << 'EOF'
      [Autologin]
      User=ubuntu
      Session=lxqt.desktop

      [General]
      HaltCommand=/usr/bin/systemctl poweroff
      RebootCommand=/usr/bin/systemctl reboot

      [Theme]
      Current=breeze
      EOF

    # Configure LXQt desktop settings
    - |
      mkdir -p /target/etc/skel/.config/lxqt
      cat > /target/etc/skel/.config/lxqt/session.conf << 'EOF'
      [General]
      __userfile__=true

      [Environment]
      BROWSER=firefox
      TERMINAL=qterminal

      [Keyboard]
      delay=500
      interval=30
      beep=false
      EOF

    # Configure LXQt panel for minimal resource usage
    - |
      mkdir -p /target/etc/skel/.config/lxqt
      cat > /target/etc/skel/.config/lxqt/panel.conf << 'EOF'
      [General]
      __userfile__=true

      [panel1]
      alignment=0
      animation-duration=0
      background-color=@Variant(\0\0\0\x43\0\xff\xff\0\0\0\0\0\0\0\0)
      background-image=
      desktop=0
      font-color=@Variant(\0\0\0\x43\0\xff\xff\0\0\0\0\0\0\0\0)
      hidable=false
      hide-on-overlap=false
      iconSize=22
      lineCount=1
      lockPanel=false
      opacity=100
      panelSize=32
      plugins=taskbar, tray, mount, volume, clock
      position=Bottom
      reserve-space=true
      show-delay=0
      visible-margin=true
      width=100
      width-percent=true
      EOF

    # Configure PCManFM-Qt file manager
    - |
      mkdir -p /target/etc/skel/.config/pcmanfm-qt/default
      cat > /target/etc/skel/.config/pcmanfm-qt/default/settings.conf << 'EOF'
      [Behavior]
      BookmarkOpenMethod=current_tab
      ConfirmDelete=true
      ConfirmTrash=false
      NoUsbTrash=false
      QuickExec=false
      SelectNewFiles=false
      SingleClick=false
      UseTrash=true

      [Desktop]
      BgColor=#000000
      FgColor=#ffffff
      Font="Sans,9,-1,5,50,0,0,0,0,0"
      ShadowColor=#000000
      ShowHidden=false
      SortColumn=name
      SortOrder=ascending
      Wallpaper=/usr/share/pixmaps/ubuntu-logo.png
      WallpaperMode=stretch

      [FolderView]
      BackupAsHidden=false
      BigIconSize=48
      FolderViewMode=icon
      HiddenLast=true
      ShadowHidden=true
      ShowFilter=false
      ShowFullNames=false
      ShowHidden=false
      SidePaneIconSize=24
      SmallIconSize=24
      SortCaseSensitive=false
      SortColumn=name
      SortFolderFirst=true
      SortOrder=ascending
      ThumbnailIconSize=128

      [System]
      Archiver=ark
      FallbackIconThemeName=breeze
      OnlyUserTemplates=false
      SIUnit=false
      SuCommand=sudo %s
      TemplateRunApp=false
      TemplateTypeOnce=false
      Terminal=qterminal
      EOF
    
    # Set timezone
    - curtin in-target --target=/target -- timedatectl set-timezone UTC
    
    # Configure locale
    - curtin in-target --target=/target -- locale-gen en_US.UTF-8
    - curtin in-target --target=/target -- update-locale LANG=en_US.UTF-8
    
    # Configure desktop environment for ubuntu user
    - |
      mkdir -p /target/home/<USER>/.config/lxqt
      cp -r /target/etc/skel/.config/lxqt/* /target/home/<USER>/.config/lxqt/
      chroot /target chown -R ubuntu:ubuntu /home/<USER>/.config

    # Set default target to graphical
    - curtin in-target --target=/target -- systemctl set-default graphical.target

    # Add ubuntu user to necessary groups for GUI
    - curtin in-target --target=/target -- usermod -a -G audio,video,plugdev ubuntu

    # Clean up
    - curtin in-target --target=/target -- apt-get clean
    - curtin in-target --target=/target -- rm -rf /var/lib/apt/lists/*
    - curtin in-target --target=/target -- rm -rf /tmp/*
    - curtin in-target --target=/target -- rm -rf /var/tmp/*
    
    # Create custom MOTD
    - |
      cat > /target/etc/motd << 'EOF'
       __  __ _       _                 _   _  _                 _
      |  \/  (_)     (_)               | | | || |               | |
      | .  . |_ _ __  _ _ __ ___   __ _| | | || |_ __  _   _ _ __ | |_ _   _
      | |\/| | | '_ \| | '_ ` _ \ / _` | | | || | '_ \| | | | '_ \| __| | | |
      | |  | | | | | | | | | | | | (_| | | | || | |_) | |_| | | | | |_| |_| |
      \_|  |_/_|_| |_|_|_| |_| |_|\__,_|_| |_||_|_.__/ \__,_|_| |_|\__|\__,_|

         ____                           ____  ____    ___  _  _   _   _____ ____
        / ___|  ___ _ ____   _____ _ __ |___ \|___ \  / _ \| || | | | |_   _/ ___|
        \___ \ / _ \ '__\ \ / / _ \ '__|  __) | __) || | | | || |_| |   | | \___ \
         ___) |  __/ |   \ V /  __/ |    / __/ / __/ | |_| |__   _| |___| |  ___) |
        |____/ \___|_|    \_/ \___|_|   |_____|_____| \___/   |_| |_____|_| |____/

      Welcome to Minimal Ubuntu Server 22.04 LTS with LXQt Desktop

      This is a custom minimal installation with GUI designed for:
      - Lightweight LXQt desktop environment
      - Reduced resource usage compared to full desktop
      - Essential server and desktop functionality
      - Enhanced security configuration

      Default login: ubuntu / ubuntu
      GUI: LXQt Desktop Environment
      EOF
  
  # Reboot after installation
  shutdown: reboot
