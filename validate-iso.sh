#!/bin/bash

# Minimal Ubuntu Server ISO Validation Script

ISO_FILE="minimal-ubuntu-server-22.04-lts.iso"

echo "=== Minimal Ubuntu Server 22.04 LTS ISO Validation ==="
echo

# Check if ISO file exists
if [ ! -f "$ISO_FILE" ]; then
    echo "❌ ERROR: ISO file '$ISO_FILE' not found!"
    exit 1
fi

echo "✅ ISO file found: $ISO_FILE"

# Check ISO file size
ISO_SIZE=$(stat -c%s "$ISO_FILE")
ISO_SIZE_MB=$((ISO_SIZE / 1024 / 1024))
echo "📏 ISO size: ${ISO_SIZE_MB} MB"

# Check if ISO is bootable
echo "🔍 Checking ISO structure..."

# Mount ISO temporarily to check contents
MOUNT_POINT="/tmp/iso_check_$$"
sudo mkdir -p "$MOUNT_POINT"

if sudo mount -o loop "$ISO_FILE" "$MOUNT_POINT" 2>/dev/null; then
    echo "✅ ISO can be mounted successfully"
    
    # Check for essential files
    echo "🔍 Checking essential files..."
    
    if [ -f "$MOUNT_POINT/casper/vmlinuz" ]; then
        echo "✅ <PERSON><PERSON> found"
    else
        echo "❌ Kernel not found"
    fi
    
    if [ -f "$MOUNT_POINT/casper/initrd" ]; then
        echo "✅ Initial ramdisk found"
    else
        echo "❌ Initial ramdisk not found"
    fi
    
    if [ -f "$MOUNT_POINT/boot/grub/grub.cfg" ]; then
        echo "✅ GRUB configuration found"
    else
        echo "❌ GRUB configuration not found"
    fi
    
    if [ -f "$MOUNT_POINT/user-data" ]; then
        echo "✅ Autoinstall user-data found"
    else
        echo "❌ Autoinstall user-data not found"
    fi
    
    if [ -f "$MOUNT_POINT/meta-data" ]; then
        echo "✅ Autoinstall meta-data found"
    else
        echo "❌ Autoinstall meta-data not found"
    fi
    
    # Check GRUB menu for our custom entry
    if grep -q "Minimal Ubuntu Server 22.04 LTS (Autoinstall)" "$MOUNT_POINT/boot/grub/grub.cfg"; then
        echo "✅ Custom autoinstall menu entry found"
    else
        echo "❌ Custom autoinstall menu entry not found"
    fi
    
    # List some key directories
    echo "📁 ISO contents:"
    ls -la "$MOUNT_POINT" | head -10
    
    sudo umount "$MOUNT_POINT"
    sudo rmdir "$MOUNT_POINT"
else
    echo "❌ ERROR: Cannot mount ISO file"
    exit 1
fi

# Check ISO integrity using file command
echo "🔍 Checking ISO format..."
file "$ISO_FILE"

echo
echo "=== Validation Summary ==="
echo "✅ ISO file created successfully: $ISO_FILE"
echo "📏 Size: ${ISO_SIZE_MB} MB"
echo "🚀 Ready for deployment!"
echo
echo "=== Usage Instructions ==="
echo "1. Burn this ISO to a USB drive or CD/DVD"
echo "2. Boot from the media"
echo "3. Select 'Minimal Ubuntu Server 22.04 LTS (Autoinstall)' from the GRUB menu"
echo "4. The system will automatically install with minimal configuration"
echo "5. Default login: ubuntu / ubuntu"
echo
echo "=== Features ==="
echo "• Minimal package selection for reduced footprint"
echo "• Automated installation with autoinstall"
echo "• SSH server enabled by default"
echo "• UFW firewall configured"
echo "• systemd-networkd for network management"
echo "• Chrony for time synchronization"
echo "• Essential system tools only"
echo
echo "=== Security Notes ==="
echo "• Change default password after first login"
echo "• Configure SSH keys for secure access"
echo "• Review and adjust firewall rules as needed"
echo "• Keep system updated with 'apt update && apt upgrade'"
