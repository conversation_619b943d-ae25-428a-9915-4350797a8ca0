#cloud-config
autoinstall:
  version: 1
  
  # Locale and keyboard
  locale: en_US.UTF-8
  keyboard:
    layout: us
    variant: ''
  
  # Network configuration
  network:
    network:
      version: 2
      ethernets:
        any:
          match:
            name: "e*"
          dhcp4: true
          dhcp6: false
  
  # Storage configuration - use entire disk
  storage:
    layout:
      name: direct
    swap:
      size: 0
  
  # Identity configuration
  identity:
    hostname: minimal-ubuntu
    username: ubuntu
    password: '$6$rounds=4096$saltsalt$L9tjczoIVjIaLZL4HSWiFQWJjKJBWzVtQFJwO/Vo7.rMHgqmKfF8L6Rn.rLxjIJhd7EdAoq1QU2KXLmvF8E4/'  # password: ubuntu
    realname: Ubuntu User
  
  # SSH configuration
  ssh:
    install-server: true
    allow-pw: true
    authorized-keys: []
  
  # Package selection - minimal
  packages:
    - openssh-server
    - curl
    - wget
    - nano
    - vim-tiny
    - htop
    - net-tools
    - rsync
    - ufw
    - chrony
    - lshw
    - unzip
  
  # Packages to remove
  package_update: false
  package_upgrade: false
  
  # User configuration
  user-data:
    users:
      - name: ubuntu
        groups: [adm, sudo]
        lock_passwd: false
        sudo: ['ALL=(ALL) NOPASSWD:ALL']
        shell: /bin/bash
  
  # Late commands for customization
  late-commands:
    # Remove unnecessary packages
    - curtin in-target --target=/target -- apt-get remove -y snapd network-manager
    - curtin in-target --target=/target -- apt-get autoremove -y
    - curtin in-target --target=/target -- apt-get autoclean
    
    # Configure firewall
    - curtin in-target --target=/target -- ufw --force enable
    - curtin in-target --target=/target -- ufw default deny incoming
    - curtin in-target --target=/target -- ufw default allow outgoing
    - curtin in-target --target=/target -- ufw allow ssh
    
    # Configure systemd-networkd
    - curtin in-target --target=/target -- systemctl enable systemd-networkd
    - curtin in-target --target=/target -- systemctl enable systemd-resolved
    - curtin in-target --target=/target -- systemctl enable chrony
    
    # Create network configuration
    - |
      cat > /target/etc/systemd/network/20-dhcp.network << 'EOF'
      [Match]
      Name=en*
      
      [Network]
      DHCP=yes
      EOF
    
    # Set timezone
    - curtin in-target --target=/target -- timedatectl set-timezone UTC
    
    # Configure locale
    - curtin in-target --target=/target -- locale-gen en_US.UTF-8
    - curtin in-target --target=/target -- update-locale LANG=en_US.UTF-8
    
    # Clean up
    - curtin in-target --target=/target -- apt-get clean
    - curtin in-target --target=/target -- rm -rf /var/lib/apt/lists/*
    - curtin in-target --target=/target -- rm -rf /tmp/*
    - curtin in-target --target=/target -- rm -rf /var/tmp/*
    
    # Create custom MOTD
    - |
      cat > /target/etc/motd << 'EOF'
       __  __ _       _                 _   _  _                 _         
      |  \/  (_)     (_)               | | | || |               | |        
      | .  . |_ _ __  _ _ __ ___   __ _| | | || |_ __  _   _ _ __ | |_ _   _ 
      | |\/| | | '_ \| | '_ ` _ \ / _` | | | || | '_ \| | | | '_ \| __| | | |
      | |  | | | | | | | | | | | | (_| | | | || | |_) | |_| | | | | |_| |_| |
      \_|  |_/_|_| |_|_|_| |_| |_|\__,_|_| |_||_|_.__/ \__,_|_| |_|\__|\__,_|
                                                                            
         ____                           ____  ____    ___  _  _   _   _____ ____  
        / ___|  ___ _ ____   _____ _ __ |___ \|___ \  / _ \| || | | | |_   _/ ___| 
        \___ \ / _ \ '__\ \ / / _ \ '__|  __) | __) || | | | || |_| |   | | \___ \ 
         ___) |  __/ |   \ V /  __/ |    / __/ / __/ | |_| |__   _| |___| |  ___) |
        |____/ \___|_|    \_/ \___|_|   |_____|_____| \___/   |_| |_____|_| |____/ 
      
      Welcome to Minimal Ubuntu Server 22.04 LTS
      
      This is a custom minimal installation designed for:
      - Reduced resource usage
      - Faster boot times  
      - Essential server functionality only
      - Enhanced security configuration
      
      Default login: ubuntu / ubuntu
      EOF
  
  # Reboot after installation
  shutdown: reboot
