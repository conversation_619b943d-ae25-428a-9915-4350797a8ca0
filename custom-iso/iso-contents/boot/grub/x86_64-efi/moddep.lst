xfs: fshelp
fshelp:
videotest_checksum: video_fb font functional_test
relocator: mmap
procfs: archelp
offsetio:
lsefisystab:
loadbios:
gcry_seed: crypto
ufs2:
tr: extcmd
lsacpi: extcmd acpi
json:
gfxterm_background: extcmd gfxterm bitmap video video_colors bitmap_scale
file: offsetio extcmd elf macho
xnu_uuid: gcry_md5
part_sunpc:
extcmd:
echo: extcmd
btrfs: gzio lzopio raid6rec zstd
read:
priority_queue:
lsefimmap:
gcry_dsa: mpi pgp
ctz_test: functional_test
cbls: cbtable
zfs: gzio
progress: normal
minix:
setjmp:
macbless: disk
hfspluscomp: gzio hfsplus
gcry_rsa: mpi pgp
cat: extcmd
videotest: font video gfxmenu
sleep: extcmd normal
reiserfs: fshelp
lspci: extcmd
div:
video_cirrus: video_fb video
testload:
search_fs_uuid:
part_dvh:
gcry_md5: crypto
gcry_arcfour: crypto
ufs1_be:
part_acorn:
iorw: extcmd
gfxterm: font video
cpio_be: archelp
scsi:
memdisk:
fixvideo:
crc64: crypto
ata: scsi
tpm:
multiboot2: relocator boot net acpi linux video mmap
gcry_idea: crypto
cpuid: extcmd
cmp_test: functional_test
video_fb:
sfs: fshelp
zfscrypt: extcmd zfs crypto pbkdf2 gcry_rijndael gcry_sha1
video_bochs: video_fb video
udf: fshelp
datehook: datetime
adler32: crypto
setjmp_test: setjmp functional_test
ntfscomp: ntfs
nativedisk:
eval: normal
videoinfo: video
pbkdf2: crypto
password_pbkdf2: crypto pbkdf2 normal gcry_sha512
font: bufio video
datetime:
crypto:
strtoull_test: functional_test
pcidump: extcmd
mdraid09_be: diskfilter
afsplitter: crypto
normal: extcmd datetime crypto boot net bufio gettext terminal
tftp: net
gcry_whirlpool: crypto
fat: fshelp
setpci: extcmd
boot:
ufs1:
cpio: archelp
chain: boot net efinet
cbmemc: normal terminfo cbtable
tga: bufio bitmap
spkmodem: terminfo
odc: archelp
usbserial_pl2303: usb serial usbserial_common
regexp: extcmd normal
password: crypto normal
ntfs: fshelp
net: priority_queue datetime boot bufio
loadenv: extcmd disk
gzio: gcry_crc
ehci: boot cs5536 usb
archelp:
kernel:
usbtest: usb
linuxefi: boot linux
gptsync: disk
date: datetime
xnu: relocator extcmd boot random bitmap video mmap bitmap_scale macho
mul_test: functional_test
bufio:
acpi: extcmd mmap
png: bufio bitmap
minix2:
cs5536:
linux: relocator boot video mmap
gcry_des: crypto
gcry_blowfish: crypto
part_plan:
part_bsd: part_msdos
lvm: diskfilter
lsefi:
gcry_tiger: crypto
blocklist:
xzio: crypto
usb_keyboard: usb keylayouts
nilfs2: fshelp
lsmmap: mmap
gcry_cast5: crypto
backtrace:
appleldr: boot
testspeed: extcmd normal
squash4: fshelp gzio xzio lzopio
msdospart: disk parttool
lzopio: crypto
gfxterm_menu: procfs video_fb font normal functional_test
gcry_sha256: crypto
usb:
pata: ata
affs: fshelp
syslinuxcfg: extcmd normal
random: hexdump
part_amiga:
play:
part_apple:
mpi: crypto
keylayouts:
jpeg: bufio bitmap
gcry_rmd160: crypto
efifwsetup:
cbfs: archelp
time:
lssal:
loopback: extcmd
disk:
at_keyboard: boot keylayouts
true:
test:
part_sun:
part_msdos:
morse:
http: net
part_gpt:
luks2: json crypto pbkdf2 afsplitter cryptodisk
elf:
diskfilter:
bitmap:
cmp:
uhci: usb
ls: extcmd datetime normal
search: extcmd search_fs_uuid search_fs_file search_label
raid6rec: diskfilter
minix3_be:
memrw: extcmd
cbtime: cbtable
zstd:
raid5rec: diskfilter
rdmsr: extcmd
hello: extcmd
video:
shift_test: functional_test
minix2_be:
hashsum: extcmd crypto normal
video_colors:
mdraid1x: diskfilter
gcry_twofish: crypto
gcry_sha512: crypto
cryptodisk: procfs extcmd crypto
parttool: normal
ohci: boot cs5536 usb
mmap:
minix3:
gfxmenu: gfxterm font normal bitmap video video_colors bitmap_scale trig
dm_nv: diskfilter
search_fs_file:
luks: crypto pbkdf2 afsplitter cryptodisk
ldm: part_msdos part_gpt diskfilter
hfsplus: fshelp
hfs: fshelp
hexdump: extcmd
gettext:
gcry_rijndael: crypto
aout:
terminal:
probe: extcmd
functional_test: extcmd btrfs video_fb video
minicmd:
geli: crypto pbkdf2 gcry_sha256 cryptodisk gcry_sha512
gcry_rfc2268: crypto
f2fs: fshelp
ext2: fshelp
bsd: relocator extcmd gcry_md5 cpuid crypto boot elf video mmap aout serial
terminfo: extcmd
legacy_password_test: functional_test legacycfg
gcry_crc: crypto
gcry_camellia: crypto
bitmap_scale: bitmap
part_dfly:
efinet: net
cbtable:
usbms: scsi usb
sleep_test: datetime functional_test
div_test: div functional_test
zfsinfo: zfs
wrmsr:
newc: archelp
multiboot: relocator boot net linux video mmap
linux16: relocator boot linux video mmap
keystatus: extcmd
jfs:
gcry_md4: crypto
mdraid09: diskfilter
iso9660: fshelp
cmdline_cat_test: procfs video_fb font normal functional_test
bswap_test: functional_test
afs: fshelp
search_label:
reboot:
pgp: extcmd crypto mpi gcry_sha1
efi_gop: video_fb video
xnu_uuid_test: functional_test
test_blockarg: extcmd normal
serial: extcmd terminfo
configfile: normal
romfs: fshelp
minix_be:
help: extcmd normal
halt: acpi
gcry_sha1: crypto
usbserial_usbdebug: usb serial usbserial_common
usbserial_ftdi: usb serial usbserial_common
hdparm: extcmd hexdump
trig:
tar: archelp
legacycfg: gcry_md5 crypto normal password
ahci: ata boot
pbkdf2_test: pbkdf2 functional_test gcry_sha1
macho:
efi_uga: video_fb video
smbios: extcmd
signature_test: procfs functional_test
gcry_serpent: crypto
bfs: fshelp
usbserial_common: usb serial
exfctest: functional_test
exfat: fshelp
all_video: efi_gop efi_uga video_bochs video_cirrus
