#!/bin/bash

# Minimal Ubuntu Server with LXQt GUI ISO Validation Script

ISO_FILE="minimal-ubuntu-server-lxqt-22.04-lts.iso"

echo "=== Minimal Ubuntu Server 22.04 LTS with LXQt GUI ISO Validation ==="
echo

# Check if ISO file exists
if [ ! -f "$ISO_FILE" ]; then
    echo "❌ ERROR: ISO file '$ISO_FILE' not found!"
    exit 1
fi

echo "✅ ISO file found: $ISO_FILE"

# Check ISO file size
ISO_SIZE=$(stat -c%s "$ISO_FILE")
ISO_SIZE_MB=$((ISO_SIZE / 1024 / 1024))
echo "📏 ISO size: ${ISO_SIZE_MB} MB"

# Check if ISO is bootable
echo "🔍 Checking ISO structure..."

# Mount ISO temporarily to check contents
MOUNT_POINT="/tmp/iso_gui_check_$$"
sudo mkdir -p "$MOUNT_POINT"

if sudo mount -o loop "$ISO_FILE" "$MOUNT_POINT" 2>/dev/null; then
    echo "✅ ISO can be mounted successfully"
    
    # Check for essential files
    echo "🔍 Checking essential files..."
    
    if [ -f "$MOUNT_POINT/casper/vmlinuz" ]; then
        echo "✅ Kernel found"
    else
        echo "❌ Kernel not found"
    fi
    
    if [ -f "$MOUNT_POINT/casper/initrd" ]; then
        echo "✅ Initial ramdisk found"
    else
        echo "❌ Initial ramdisk not found"
    fi
    
    if [ -f "$MOUNT_POINT/boot/grub/grub.cfg" ]; then
        echo "✅ GRUB configuration found"
    else
        echo "❌ GRUB configuration not found"
    fi
    
    if [ -f "$MOUNT_POINT/user-data" ]; then
        echo "✅ Autoinstall user-data found"
    else
        echo "❌ Autoinstall user-data not found"
    fi
    
    if [ -f "$MOUNT_POINT/meta-data" ]; then
        echo "✅ Autoinstall meta-data found"
    else
        echo "❌ Autoinstall meta-data not found"
    fi
    
    # Check GRUB menu for our custom GUI entry
    if grep -q "LXQt GUI" "$MOUNT_POINT/boot/grub/grub.cfg"; then
        echo "✅ Custom LXQt GUI autoinstall menu entry found"
    else
        echo "❌ Custom LXQt GUI autoinstall menu entry not found"
    fi
    
    # Check for GUI packages in user-data
    if grep -q "lxqt-core" "$MOUNT_POINT/user-data"; then
        echo "✅ LXQt packages found in autoinstall configuration"
    else
        echo "❌ LXQt packages not found in autoinstall configuration"
    fi
    
    if grep -q "sddm" "$MOUNT_POINT/user-data"; then
        echo "✅ SDDM display manager found in configuration"
    else
        echo "❌ SDDM display manager not found in configuration"
    fi
    
    if grep -q "firefox" "$MOUNT_POINT/user-data"; then
        echo "✅ Firefox browser found in configuration"
    else
        echo "❌ Firefox browser not found in configuration"
    fi
    
    # List some key directories
    echo "📁 ISO contents:"
    ls -la "$MOUNT_POINT" | head -10
    
    sudo umount "$MOUNT_POINT"
    sudo rmdir "$MOUNT_POINT"
else
    echo "❌ ERROR: Cannot mount ISO file"
    exit 1
fi

# Check ISO integrity using file command
echo "🔍 Checking ISO format..."
file "$ISO_FILE"

echo
echo "=== Validation Summary ==="
echo "✅ LXQt GUI ISO file created successfully: $ISO_FILE"
echo "📏 Size: ${ISO_SIZE_MB} MB"
echo "🚀 Ready for deployment!"
echo
echo "=== Usage Instructions ==="
echo "1. Burn this ISO to a USB drive or CD/DVD"
echo "2. Boot from the media"
echo "3. Select 'Minimal Ubuntu Server 22.04 LTS with LXQt GUI (Autoinstall)' from the GRUB menu"
echo "4. The system will automatically install with LXQt desktop environment"
echo "5. Default login: ubuntu / ubuntu (auto-login enabled)"
echo "6. LXQt desktop will start automatically after installation"
echo
echo "=== LXQt Desktop Features ==="
echo "• Lightweight LXQt desktop environment"
echo "• SDDM display manager with auto-login"
echo "• PCManFM-Qt file manager"
echo "• QTerminal terminal emulator"
echo "• Featherpad text editor"
echo "• Firefox web browser"
echo "• QPDFView PDF viewer"
echo "• Network Manager for GUI network configuration"
echo "• PulseAudio for audio support"
echo "• Ark archive manager"
echo "• GParted disk utility"
echo "• Synaptic package manager"
echo
echo "=== System Features ==="
echo "• SSH server enabled by default"
echo "• UFW firewall configured"
echo "• Chrony for time synchronization"
echo "• Essential system tools included"
echo "• Optimized for minimal resource usage"
echo
echo "=== Security Notes ==="
echo "• Change default password after first login"
echo "• Configure SSH keys for secure access"
echo "• Review and adjust firewall rules as needed"
echo "• Keep system updated with 'apt update && apt upgrade'"
echo "• Auto-login is enabled for convenience - disable if needed for security"
